<?xml version="1.0" encoding="UTF-8" ?>
<odoo>
    <!--        Inherited the Res Config Settings to add new field on it-->
    <record id="res_config_settings_view_form_purchase" model="ir.ui.view">
        <field name="name">
            res.config.settings.view.form.inherit.purchase.down.payment
        </field>
        <field name="model">res.config.settings</field>
        <field name="priority" eval="95"/>
        <field name="inherit_id" ref="purchase.res_config_settings_view_form_purchase"/>
        <field name="arch" type="xml">
            <xpath expr="//block[@name='invoicing_settings_container']" position="inside">
                <div class="col-12 col-lg-6 o_setting_box">
                    <div class="o_setting_left_pane"/>
                    <div class="o_setting_right_pane">
                        <span class="o_form_label">Down Payment</span>
                        <div class="text-muted">
                            Product used for down payments in purchase bill
                        </div>
                        <div class="text-muted">
                            <field name="po_deposit_default_product_id"/>
                        </div>
                    </div>
                </div>
            </xpath>
        </field>
    </record>
</odoo>
