<?xml version="1.0" encoding="utf-8"?>
<odoo>
    <!--   Inherited the Purchase Order form view to add a new button -->
    <record id="purchase_order_form" model="ir.ui.view">
        <field name="name">
            purchase.order.view.form.inherit.purchase.down.payment
        </field>
        <field name="model">purchase.order</field>
        <field name="inherit_id" ref="purchase.purchase_order_form"/>
        <field name="arch" type="xml">
            <xpath expr="//header/button[@name='action_create_invoice']"
                   position="replace">
                <button name="%(purchase_down_payment.purchase_order_advance_payment_action)d"
                        string="Create Bill"
                        invisible="state not in ('purchase', 'done') or invoice_status in ('no', 'invoiced')"
                        type="action" data-hotkey="w"/>
            </xpath>
        </field>
    </record>
</odoo>
