<?xml version="1.0" encoding="utf-8"?>
<odoo>
    <data>
        <record id="hr_expense_sheet_inherit_form_view" model="ir.ui.view">
            <field name="name">hr.expense.sheet.inherit.form.view</field>
            <field name="model">hr.expense.sheet</field>
            <field name="inherit_id" ref="hr_expense.view_hr_expense_sheet_form"/>
            <field name="arch" type="xml">

                <xpath expr="//div[@class='oe_title']" position="before">
                    <div class="oe_title">
                        <h1>
                            <field name="expense_seq" readonly="1"/>
                        </h1>
                    </div>
                </xpath>

<!--                <xpath expr="//button[@name='action_sheet_move_create']" position="attributes">-->
<!--                    <attribute name="invisible">1</attribute>-->
<!--                </xpath>-->

<!--                <xpath expr="//button[@name='action_register_payment']" position="after">-->
<!--                    <button name="create_bil_from_expense" string="Create Bill" type="object"-->
<!--                       class="oe_highlight" attrs="{'invisible': ['|', ('state', '!=', 'approve'), ('bill_count', '>', 0)]}"/>-->
<!--                </xpath>-->

                <xpath expr="//button[@name='action_submit_sheet']" position="replace">
                    <button name="action_submit_sheet" invisible="state != 'draft'" string="Submit to Check" type="object"
                            class="oe_highlight o_expense_sheet_submit" data-hotkey="l"
                            groups="meta_approval_matrix_cross_world.user_approval"/>
                </xpath>

                <xpath expr="//button[@name='action_approve_expense_sheets']" position="replace">
                    <button name="action_approve_expense_sheets" string="Approve" type="object" data-hotkey="v"
                        invisible="not can_approve or state != 'submit_another'"
                        class="oe_highlight o_expense_sheet_approve" groups="meta_approval_matrix_cross_world.approved_approval"/>

                    <button name="action_submit_sheet_another" invisible="state != 'submit'" string="Submit to Approve"
                            type="object" class="oe_highlight o_expense_sheet_submit" data-hotkey="l"
                            groups="meta_approval_matrix_cross_world.checker_approval"/>

                </xpath>


                <xpath expr="//button[@name='action_open_account_moves']" position="before">
                    <button type="object" name="view_created_bill" class="oe_stat_button" invisible="bill_count == 0" icon="fa-list">
                        <field name="bill_count" widget="statinfo" string="Bill"/>
                    </button>
                </xpath>
                <!-- attrs="{'invisible': ['|', ('state', '!=', 'approved'), ('bill_count', '>', 0)]}"-->
            </field>
        </record>

        <record id="hr_expense_sheet_inherit_tree_view" model="ir.ui.view">
            <field name="name">hr.expense.sheet.inherit.tree.view</field>
            <field name="model">hr.expense.sheet</field>
            <field name="inherit_id" ref="hr_expense.view_hr_expense_sheet_tree"/>
            <field name="arch" type="xml">
                <field name="employee_id" position="before">
                    <field name="expense_seq"/>
                </field>
            </field>
        </record>

        <record id="hr_expense_sheet_inherit_search_view" model="ir.ui.view">
            <field name="name">hr.expense.sheet.inherit.search.view</field>
            <field name="model">hr.expense.sheet</field>
            <field name="inherit_id" ref="hr_expense.hr_expense_sheet_view_search"/>
            <field name="arch" type="xml">
                <field name="name" position="before">
                    <field string="Reference" name="expense_seq" filter_domain="[('expense_seq', 'ilike', self)]"/>
                </field>
            </field>
        </record>

        <record id="hr_expense_inherit_form_view" model="ir.ui.view">
            <field name="name">hr.expense.inherit.form.view</field>
            <field name="model">hr.expense</field>
            <field name="inherit_id" ref="hr_expense.hr_expense_view_form"/>
            <field name="arch" type="xml">
                <field name="tax_ids" position="after">
                    <field name="expense_for" options="{'no_create': True, 'no_create_edit': True}"/>
                </field>

                <xpath expr="//div[@class='oe_title']" position="before">
                    <div class="oe_title">
                        <h1>
                            <field name="exp_seq" readonly="1"/>
                        </h1>
                    </div>
                </xpath>

            </field>
        </record>

        <record id="hr_expense_inherit_tree_view" model="ir.ui.view">
            <field name="name">hr.expense.inherit.tree.view</field>
            <field name="model">hr.expense</field>
            <field name="inherit_id" ref="hr_expense.hr_expense_view_expenses_analysis_tree"/>
            <field name="arch" type="xml">
                <field name="date" position="after">
                    <field name="exp_seq"/>
                </field>
            </field>
        </record>

        <record id="hr_expense_inherit_search_view" model="ir.ui.view">
            <field name="name">hr.expense.inherit.search.view</field>
            <field name="model">hr.expense</field>
            <field name="inherit_id" ref="hr_expense.hr_expense_view_search"/>
            <field name="arch" type="xml">
                <field name="name" position="before">
                    <field string="Reference" name="exp_seq" filter_domain="[('exp_seq', 'ilike', self)]"/>
<!--                    <field name="exp_seq"/>-->
                </field>
            </field>
        </record>

        <record id="action_account_move_approve_list" model="ir.actions.server">
            <field name="name">Direct Approve</field>
            <field name="groups_id" eval="[(4, ref('account.group_account_invoice'))]"/>
            <field name="model_id" ref="account.model_account_move"/>
            <field name="binding_model_id" ref="account.model_account_move"/>
            <field name="binding_view_types">list</field>
            <field name="state">code</field>
            <field name="code">
                if records:
                    action = records.post_all_draft_journal()
            </field>
        </record>

    </data>
</odoo>